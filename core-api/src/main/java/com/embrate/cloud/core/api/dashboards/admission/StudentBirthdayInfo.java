package com.embrate.cloud.core.api.dashboards.admission;

import java.util.UUID;

/**
 * Represents basic birthday information for a student with class details
 * <AUTHOR>
 */
public class StudentBirthdayInfo {

    private final UUID studentId;
    private final String studentName;
    private final String admissionNumber;
    private final Integer birthdayDate;
    private final String className;
    private final String sessionName;

    public StudentBirthdayInfo(UUID studentId, String studentName, String admissionNumber, Integer birthdayDate, String className, String sessionName) {
        this.studentId = studentId;
        this.studentName = studentName;
        this.admissionNumber = admissionNumber;
        this.birthdayDate = birthdayDate;
        this.className = className;
        this.sessionName = sessionName;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public String getStudentName() {
        return studentName;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public Integer getBirthdayDate() {
        return birthdayDate;
    }

    public String getClassName() {
        return className;
    }

    public String getSessionName() {
        return sessionName;
    }

    @Override
    public String toString() {
        return "StudentBirthdayInfo{" +
                "studentId=" + studentId +
                ", studentName='" + studentName + '\'' +
                ", admissionNumber='" + admissionNumber + '\'' +
                ", birthdayDate=" + birthdayDate +
                ", className='" + className + '\'' +
                ", sessionName='" + sessionName + '\'' +
                '}';
    }
}
