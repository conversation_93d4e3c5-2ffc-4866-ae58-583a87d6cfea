package com.embrate.cloud.core.lib.dashboards.fees;

import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.attendance.*;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;
import com.embrate.cloud.core.api.dashboards.fees.*;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.dao.tier.dashboard.admission.UserDashboardDao;
import com.embrate.cloud.dao.tier.dashboard.attendance.AttendanceDashboardDao;
import com.embrate.cloud.dao.tier.dashboard.fees.FeesDashboardDao;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.core.lib.attendance.AttendanceManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.utils.institute.DashboardUtils.*;

/**
 * <AUTHOR>
 **/
public class FeesDashboardManager {

	private static final Logger logger = LogManager.getLogger(FeesDashboardManager.class);

	private final InstituteManager instituteManager;

	private final FeesDashboardDao feesDashboardDao;

	public FeesDashboardManager(InstituteManager instituteManager, FeesDashboardDao feesDashboardDao) {
		this.instituteManager = instituteManager;
		this.feesDashboardDao = feesDashboardDao;
	}

	public StudentFeesOrgStats getStudentFeesOrgStats(UUID organizationId, String selectedInstituteIds,
													  UUID userId, int startDate, int endDate) {
		validatePayload(organizationId, selectedInstituteIds, userId, startDate, endDate);

		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));
		List<FeeCollectionByPaymentModeFeeHead> feeCollectionByPaymentModeFeeHeads = feesDashboardDao.getFeesCollectedByTransactionModeFeeHead(institutes, startDate, endDate);
		if (CollectionUtils.isEmpty(feeCollectionByPaymentModeFeeHeads)) {
			// Create objects with zero values for all institutes instead of empty lists
			return createEmptyStudentFeesOrgStats(institutes, instituteNameMap);
		}

		Map<Integer, Map<TransactionMode, Double>> institutePaymentModeMap = new HashMap<>();
		Map<Integer, Map<String, Double>> instituteFeeHeadMap = new HashMap<>();
		Map<Integer, Double> instituteCollectedAmount = new HashMap<>();

		Map<TransactionMode, Double> orgPaymentModeMap = new HashMap<>();
		Map<String, Double> orgFeeHeadMap = new HashMap<>();
		double totalAmount = 0d;
		for (FeeCollectionByPaymentModeFeeHead feeCollectionByPaymentModeFeeHead : feeCollectionByPaymentModeFeeHeads) {
			totalAmount += feeCollectionByPaymentModeFeeHead.getTotalAmount();
			populateTransactionModeFeeHeadMaps(feeCollectionByPaymentModeFeeHead, institutePaymentModeMap, instituteFeeHeadMap, instituteCollectedAmount, orgPaymentModeMap, orgFeeHeadMap);
		}

		List<PaymentModeCollection> orgPaymentModeCollections = new ArrayList<>();
		List<FeeHeadCollection> orgFeeHeadCollections = new ArrayList<>();
		for(TransactionMode transactionMode : orgPaymentModeMap.keySet()) {
			orgPaymentModeCollections.add(new PaymentModeCollection(transactionMode, orgPaymentModeMap.get(transactionMode)));
		}

		for(String feeHead : orgFeeHeadMap.keySet()) {
			orgFeeHeadCollections.add(new FeeHeadCollection(feeHead, orgFeeHeadMap.get(feeHead)));
		}

		List<StudentFeesInstituteStats> instituteStats = new ArrayList<>();
		for (Integer institute : institutes) {
			List<PaymentModeCollection> paymentModeAmounts = new ArrayList<>();
			List<FeeHeadCollection> feeHeadCollections = new ArrayList<>();
			Map<TransactionMode, Double> institutePaymentModeCollection = institutePaymentModeMap.get(institute);
			Map<String, Double> instituteFeeHeadCollection = instituteFeeHeadMap.get(institute);
			if (institutePaymentModeCollection != null) {
				for(TransactionMode transactionMode : institutePaymentModeCollection.keySet()) {
					paymentModeAmounts.add(new PaymentModeCollection(transactionMode, institutePaymentModeCollection.get(transactionMode)));
				}
			}
			if (instituteFeeHeadCollection != null) {
				for(String feeHead : instituteFeeHeadCollection.keySet()) {
					feeHeadCollections.add(new FeeHeadCollection(feeHead, instituteFeeHeadCollection.get(feeHead)));
				}
			}
			Double instituteAmount = instituteCollectedAmount.get(institute);
			instituteStats.add(new StudentFeesInstituteStats(institute, instituteNameMap.get(institute), paymentModeAmounts, feeHeadCollections, instituteAmount == null ? 0d : instituteAmount));
		}
		return new StudentFeesOrgStats(orgPaymentModeCollections, orgFeeHeadCollections, totalAmount, instituteStats);
	}

	private static void populateTransactionModeFeeHeadMaps(FeeCollectionByPaymentModeFeeHead feeCollectionByPaymentModeFeeHead,
														   Map<Integer, Map<TransactionMode, Double>> institutePaymentModeMap,
														   Map<Integer, Map<String, Double>> instituteFeeHeadMap,
														   Map<Integer, Double> instituteCollectedAmount,
														   Map<TransactionMode, Double> orgPaymentModeMap,
														   Map<String, Double> orgFeeHeadMap) {
		int instituteId = feeCollectionByPaymentModeFeeHead.getInstituteId();
		if (!institutePaymentModeMap.containsKey(instituteId)) {
			institutePaymentModeMap.put(instituteId, new HashMap<>());
		}
		if (!institutePaymentModeMap.get(instituteId).containsKey(feeCollectionByPaymentModeFeeHead.getMode())) {
			institutePaymentModeMap.get(instituteId).put(feeCollectionByPaymentModeFeeHead.getMode(), 0d);
		}
		institutePaymentModeMap.get(instituteId).put(feeCollectionByPaymentModeFeeHead.getMode(), institutePaymentModeMap.get(instituteId).get(feeCollectionByPaymentModeFeeHead.getMode()) + feeCollectionByPaymentModeFeeHead.getTotalAmount());

		if (!instituteFeeHeadMap.containsKey(instituteId)) {
			instituteFeeHeadMap.put(instituteId, new HashMap<>());
		}
		if (!instituteFeeHeadMap.get(instituteId).containsKey(feeCollectionByPaymentModeFeeHead.getFeeHead())) {
			instituteFeeHeadMap.get(instituteId).put(feeCollectionByPaymentModeFeeHead.getFeeHead(), 0d);
		}
		instituteFeeHeadMap.get(instituteId).put(feeCollectionByPaymentModeFeeHead.getFeeHead(), instituteFeeHeadMap.get(instituteId).get(feeCollectionByPaymentModeFeeHead.getFeeHead()) + feeCollectionByPaymentModeFeeHead.getTotalAmount());

		if (!instituteCollectedAmount.containsKey(instituteId)) {
			instituteCollectedAmount.put(instituteId, 0d);
		}
		instituteCollectedAmount.put(instituteId, instituteCollectedAmount.get(instituteId) + feeCollectionByPaymentModeFeeHead.getTotalAmount());

		//Org stats
		if (!orgPaymentModeMap.containsKey(feeCollectionByPaymentModeFeeHead.getMode())) {
			orgPaymentModeMap.put(feeCollectionByPaymentModeFeeHead.getMode(), 0d);
		}
		orgPaymentModeMap.put(feeCollectionByPaymentModeFeeHead.getMode(), orgPaymentModeMap.get(feeCollectionByPaymentModeFeeHead.getMode()) + feeCollectionByPaymentModeFeeHead.getTotalAmount());

		if (!orgFeeHeadMap.containsKey(feeCollectionByPaymentModeFeeHead.getFeeHead())) {
			orgFeeHeadMap.put(feeCollectionByPaymentModeFeeHead.getFeeHead(), 0d);
		}
		orgFeeHeadMap.put(feeCollectionByPaymentModeFeeHead.getFeeHead(), orgFeeHeadMap.get(feeCollectionByPaymentModeFeeHead.getFeeHead()) + feeCollectionByPaymentModeFeeHead.getTotalAmount());
	}
}