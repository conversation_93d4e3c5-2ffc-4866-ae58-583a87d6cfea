package com.embrate.cloud.core.lib.dashboards.admission;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentAdmissionOrgStats;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayInfo;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayStats;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;
import com.embrate.cloud.dao.tier.dashboard.admission.UserDashboardDao;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.utils.institute.DashboardUtils.*;

/**
 * <AUTHOR>
 **/
public class UserDashboardManager {

	private static final Logger logger = LogManager.getLogger(UserDashboardManager.class);

	private final InstituteManager instituteManager;

	private final UserDashboardDao userDashboardDao;

	public UserDashboardManager(InstituteManager instituteManager, UserDashboardDao userDashboardDao) {
		this.instituteManager = instituteManager;
		this.userDashboardDao = userDashboardDao;
	}

	public StudentAdmissionOrgStats getStudentAdmissionOrgStats(UUID organizationId, String selectedInstituteIds,
																UUID userId, int startDate, int endDate) {

		validatePayload(organizationId, selectedInstituteIds, userId, startDate, endDate);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		List<StudentAdmTCCount> newAdmissionsAndTCCountList = userDashboardDao.getNewAdmissionsAndTCCountByDateRange(institutes, startDate, endDate);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));

		List<InstituteValue> newAdmissions = new ArrayList<>();
		List<InstituteValue> tcIssued = new ArrayList<>();
		int totalNewAdmission = 0;
		int totalTCIssued = 0;

		// Create a map to track which institutes have data
		Map<Integer, StudentAdmTCCount> instituteDataMap = new HashMap<>();
		for (StudentAdmTCCount studentAdmTCCount : newAdmissionsAndTCCountList) {
			instituteDataMap.put(studentAdmTCCount.getInstituteId(), studentAdmTCCount);
			totalNewAdmission += studentAdmTCCount.getAdmissionCount();
			totalTCIssued += studentAdmTCCount.getRelieveCount();
		}

		// Create InstituteValue objects for all institutes, with zero values for institutes without data
		for (Integer institute : institutes) {
			StudentAdmTCCount data = instituteDataMap.get(institute);
			if (data != null) {
				newAdmissions.add(new InstituteValue(data.getInstituteId(), instituteNameMap.get(data.getInstituteId()), data.getAdmissionCount()));
				tcIssued.add(new InstituteValue(data.getInstituteId(), instituteNameMap.get(data.getInstituteId()), data.getRelieveCount()));
			} else {
				// Create entries with zero values for institutes without data
				newAdmissions.add(new InstituteValue(institute, instituteNameMap.get(institute), 0));
				tcIssued.add(new InstituteValue(institute, instituteNameMap.get(institute), 0));
			}
		}

		return new StudentAdmissionOrgStats(newAdmissions, totalNewAdmission, tcIssued, totalTCIssued);
	}

	public StudentBirthdayStats getStudentBirthdayStats(UUID organizationId, String selectedInstituteIds, UUID userId) {
		validateBirthdayPayload(organizationId, selectedInstituteIds, userId);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);

		List<StudentBirthdayInfo> allBirthdayStudents = userDashboardDao.getBirthdayStudentsForDateRange(institutes);

		List<StudentBirthdayInfo> todayBirthdays = new ArrayList<>();
		List<StudentBirthdayInfo> upcomingBirthdays = new ArrayList<>();

		// SQL query already filters for current day and next 7 days,
		// and includes a flag to indicate today's birthdays
		for (StudentBirthdayInfo student : allBirthdayStudents) {
			if (student.isTodayBirthday()) {
				todayBirthdays.add(student);
			} else {
				// All other results from SQL are upcoming birthdays (next 7 days)
				upcomingBirthdays.add(student);
			}
		}

		return new StudentBirthdayStats(todayBirthdays, upcomingBirthdays);
	}

	private void validateBirthdayPayload(UUID organizationId, String selectedInstituteIds, UUID userId) {
		if (organizationId == null) {
			throw new RuntimeException("Invalid organization id.");
		}
		if (selectedInstituteIds == null || selectedInstituteIds.trim().isEmpty()) {
			throw new RuntimeException("Invalid institute ids.");
		}
		if (userId == null) {
			throw new RuntimeException("Invalid user id.");
		}
	}
}